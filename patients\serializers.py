from rest_framework import serializers
from .models import Patient


class PatientSerializer(serializers.ModelSerializer):
    """
    Serializer for Patient model
    """
    created_by = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = Patient
        fields = [
            'id', 'name', 'email', 'phone', 'date_of_birth', 'gender',
            'address', 'city', 'state', 'zip_code', 'blood_group',
            'allergies', 'medical_history', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']

    def validate_email(self, value):
        """
        Check that the email is unique for the current user
        """
        user = self.context['request'].user
        if Patient.objects.filter(email=value, created_by=user).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("A patient with this email already exists in your records.")
        return value


class PatientCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a new patient
    """
    class Meta:
        model = Patient
        fields = [
            'name', 'email', 'phone', 'date_of_birth', 'gender',
            'address', 'city', 'state', 'zip_code', 'blood_group',
            'allergies', 'medical_history'
        ]

    def validate_email(self, value):
        """
        Check that the email is unique for the current user
        """
        user = self.context['request'].user
        if Patient.objects.filter(email=value, created_by=user).exists():
            raise serializers.ValidationError("A patient with this email already exists in your records.")
        return value


class PatientListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing patients (minimal fields)
    """
    class Meta:
        model = Patient
        fields = [
            'id', 'name', 'email', 'phone', 'date_of_birth', 'gender',
            'city', 'state', 'created_at'
        ]
