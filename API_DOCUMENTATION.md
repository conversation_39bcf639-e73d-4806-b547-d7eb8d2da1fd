# Healthcare Backend API Documentation

## Overview
This is a comprehensive healthcare backend system built with Django and Django REST Framework. The system provides JWT-based authentication and manages patients, doctors, and their relationships.

## Base URL
```
http://127.0.0.1:8000/api/
```

## Authentication
The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## API Endpoints

### 1. Authentication APIs

#### Register User
- **URL:** `POST /api/auth/register/`
- **Description:** Register a new user
- **Authentication:** Not required
- **Request Body:**
```json
{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "username": "johndo<PERSON>",
    "password": "securepassword123",
    "password_confirm": "securepassword123"
}
```
- **Response (201 Created):**
```json
{
    "message": "User registered successfully",
    "user": {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "username": "johndo<PERSON>",
        "created_at": "2025-09-06T08:51:07.971986Z",
        "updated_at": "2025-09-06T08:51:07.972000Z"
    },
    "tokens": {
        "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

#### Login User
- **URL:** `POST /api/auth/login/`
- **Description:** Login user and get JWT tokens
- **Authentication:** Not required
- **Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "securepassword123"
}
```
- **Response (200 OK):**
```json
{
    "message": "Login successful",
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "username": "johndoe",
        "created_at": "2025-09-06T08:51:07.971986Z",
        "updated_at": "2025-09-06T08:51:07.972000Z"
    },
    "tokens": {
        "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

### 2. Patient Management APIs

#### List/Create Patients
- **URL:** `GET/POST /api/patients/`
- **Description:** List all patients (GET) or create a new patient (POST)
- **Authentication:** Required

**GET Request:**
- **Response (200 OK):**
```json
{
    "count": 2,
    "results": [
        {
            "id": 1,
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "phone": "+**********",
            "date_of_birth": "1990-05-15",
            "gender": "F",
            "city": "New York",
            "state": "NY",
            "created_at": "2025-09-06T09:00:00Z"
        }
    ]
}
```

**POST Request:**
- **Request Body:**
```json
{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+**********",
    "date_of_birth": "1990-05-15",
    "gender": "F",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "blood_group": "A+",
    "allergies": "None",
    "medical_history": "No significant history"
}
```

#### Patient Detail
- **URL:** `GET/PUT/DELETE /api/patients/<id>/`
- **Description:** Get, update, or delete a specific patient
- **Authentication:** Required

### 3. Doctor Management APIs

#### List/Create Doctors
- **URL:** `GET/POST /api/doctors/`
- **Description:** List all doctors (GET) or create a new doctor (POST)
- **Authentication:** Required

**POST Request:**
- **Request Body:**
```json
{
    "name": "Dr. Smith",
    "email": "<EMAIL>",
    "phone": "+**********",
    "gender": "M",
    "specialization": "Cardiology",
    "license_number": "MD12345",
    "years_of_experience": 10,
    "qualification": "MD, FACC",
    "clinic_address": "456 Medical Center",
    "city": "Boston",
    "state": "MA",
    "zip_code": "02101",
    "consultation_fee": 200.00,
    "available_days": "Monday, Tuesday, Wednesday",
    "available_hours": "9:00 AM - 5:00 PM"
}
```

#### Doctor Detail
- **URL:** `GET/PUT/DELETE /api/doctors/<id>/`
- **Description:** Get, update, or delete a specific doctor
- **Authentication:** Required

### 4. Patient-Doctor Mapping APIs

#### List/Create Mappings
- **URL:** `GET/POST /api/mappings/`
- **Description:** List all patient-doctor mappings (GET) or create a new mapping (POST)
- **Authentication:** Required

**POST Request:**
- **Request Body:**
```json
{
    "patient": 1,
    "doctor": 1,
    "notes": "Regular checkup assignment"
}
```

#### Get Patient's Doctors
- **URL:** `GET /api/mappings/<patient_id>/`
- **Description:** Get all doctors assigned to a specific patient
- **Authentication:** Required

#### Remove Doctor from Patient
- **URL:** `DELETE /api/mappings/<mapping_id>/`
- **Description:** Remove a doctor from a patient
- **Authentication:** Required

## Error Responses

All error responses follow this format:
```json
{
    "error": true,
    "message": "Error description",
    "details": {
        "field": ["Error details"]
    },
    "status_code": 400
}
```

## Status Codes
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Resource deleted successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Permission denied
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

## Testing with cURL

### Register a user:
```bash
curl -X POST http://127.0.0.1:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","username":"testuser","password":"testpass123","password_confirm":"testpass123"}'
```

### Login:
```bash
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpass123"}'
```

### Create a patient (with token):
```bash
curl -X POST http://127.0.0.1:8000/api/patients/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{"name":"Jane Doe","email":"<EMAIL>","phone":"+**********","date_of_birth":"1990-01-01","gender":"F","address":"123 Main St","city":"Boston","state":"MA","zip_code":"02101"}'
```

## Testing with Python Script

Run the included test script to verify all endpoints:
```bash
python test_api.py
```

This script will:
1. Register a new user
2. Login the user
3. Create a patient
4. Create a doctor
5. Map the patient to the doctor

## Database Schema

### User Model (Custom)
- id (Primary Key)
- name
- email (Unique)
- username
- password (Hashed)
- created_at
- updated_at

### Patient Model
- id (Primary Key)
- name
- email
- phone
- date_of_birth
- gender
- address, city, state, zip_code
- blood_group
- allergies
- medical_history
- created_by (Foreign Key to User)
- created_at
- updated_at

### Doctor Model
- id (Primary Key)
- name
- email
- phone
- gender
- specialization
- license_number (Unique)
- years_of_experience
- qualification
- clinic_address, city, state, zip_code
- consultation_fee
- available_days
- available_hours
- created_by (Foreign Key to User)
- created_at
- updated_at

### PatientDoctorMapping Model
- id (Primary Key)
- patient (Foreign Key to Patient)
- doctor (Foreign Key to Doctor)
- assigned_by (Foreign Key to User)
- assigned_date
- notes
- is_active
