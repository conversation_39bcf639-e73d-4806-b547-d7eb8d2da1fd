from rest_framework import serializers
from .models import PatientDoctorMapping
from patients.models import Patient
from doctors.models import Doctor
from patients.serializers import PatientListSerializer
from doctors.serializers import DoctorListSerializer


class PatientDoctorMappingSerializer(serializers.ModelSerializer):
    """
    Serializer for PatientDoctorMapping model
    """
    patient_details = PatientListSerializer(source='patient', read_only=True)
    doctor_details = DoctorListSerializer(source='doctor', read_only=True)
    assigned_by = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = PatientDoctorMapping
        fields = [
            'id', 'patient', 'doctor', 'patient_details', 'doctor_details',
            'assigned_by', 'assigned_date', 'notes', 'is_active'
        ]
        read_only_fields = ['id', 'assigned_by', 'assigned_date']

    def validate(self, attrs):
        """
        Validate that the patient belongs to the current user
        """
        request = self.context.get('request')
        patient = attrs.get('patient')
        
        if patient and patient.created_by != request.user:
            raise serializers.ValidationError("You can only assign doctors to your own patients.")
        
        return attrs


class PatientDoctorMappingCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a new patient-doctor mapping
    """
    class Meta:
        model = PatientDoctorMapping
        fields = ['patient', 'doctor', 'notes']

    def validate(self, attrs):
        """
        Validate that the patient belongs to the current user and mapping doesn't exist
        """
        request = self.context.get('request')
        patient = attrs.get('patient')
        doctor = attrs.get('doctor')
        
        if patient and patient.created_by != request.user:
            raise serializers.ValidationError("You can only assign doctors to your own patients.")
        
        # Check if mapping already exists
        if PatientDoctorMapping.objects.filter(patient=patient, doctor=doctor, is_active=True).exists():
            raise serializers.ValidationError("This doctor is already assigned to this patient.")
        
        return attrs


class PatientDoctorMappingListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing patient-doctor mappings (minimal fields)
    """
    patient_name = serializers.CharField(source='patient.name', read_only=True)
    doctor_name = serializers.CharField(source='doctor.name', read_only=True)
    doctor_specialization = serializers.CharField(source='doctor.specialization', read_only=True)
    
    class Meta:
        model = PatientDoctorMapping
        fields = [
            'id', 'patient', 'doctor', 'patient_name', 'doctor_name',
            'doctor_specialization', 'assigned_date', 'is_active'
        ]
