from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
import re
from datetime import date


def validate_phone_number(value):
    """
    Validate phone number format
    """
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_regex(value)


def validate_email_format(value):
    """
    Validate email format
    """
    email_regex = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    if not email_regex.match(value):
        raise ValidationError('Enter a valid email address.')


def validate_date_of_birth(value):
    """
    Validate date of birth (should not be in the future)
    """
    if value > date.today():
        raise ValidationError('Date of birth cannot be in the future.')


def validate_positive_number(value):
    """
    Validate that a number is positive
    """
    if value <= 0:
        raise ValidationError('This field must be a positive number.')


def validate_license_number(value):
    """
    Validate medical license number format
    """
    if not value or len(value) < 5:
        raise ValidationError('License number must be at least 5 characters long.')
    
    # Check for alphanumeric characters
    if not re.match(r'^[A-Za-z0-9]+$', value):
        raise ValidationError('License number can only contain letters and numbers.')


def validate_zip_code(value):
    """
    Validate ZIP code format
    """
    zip_regex = re.compile(r'^\d{5}(-\d{4})?$')
    if not zip_regex.match(value):
        raise ValidationError('Enter a valid ZIP code (e.g., 12345 or 12345-6789).')


def validate_consultation_fee(value):
    """
    Validate consultation fee
    """
    if value < 0:
        raise ValidationError('Consultation fee cannot be negative.')
    if value > 10000:
        raise ValidationError('Consultation fee seems too high. Please verify.')


def validate_years_of_experience(value):
    """
    Validate years of experience
    """
    if value < 0:
        raise ValidationError('Years of experience cannot be negative.')
    if value > 70:
        raise ValidationError('Years of experience seems too high. Please verify.')


def validate_blood_group(value):
    """
    Validate blood group
    """
    valid_blood_groups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
    if value and value not in valid_blood_groups:
        raise ValidationError(f'Invalid blood group. Valid options are: {", ".join(valid_blood_groups)}')


def validate_gender(value):
    """
    Validate gender choice
    """
    valid_genders = ['M', 'F', 'O']
    if value not in valid_genders:
        raise ValidationError('Invalid gender choice.')


def validate_name(value):
    """
    Validate name field
    """
    if not value or len(value.strip()) < 2:
        raise ValidationError('Name must be at least 2 characters long.')
    
    # Check for valid characters (letters, spaces, hyphens, apostrophes)
    if not re.match(r"^[a-zA-Z\s\-']+$", value):
        raise ValidationError('Name can only contain letters, spaces, hyphens, and apostrophes.')


def validate_specialization(value):
    """
    Validate medical specialization
    """
    if not value or len(value.strip()) < 3:
        raise ValidationError('Specialization must be at least 3 characters long.')
    
    # Check for valid characters
    if not re.match(r"^[a-zA-Z\s\-&,()]+$", value):
        raise ValidationError('Specialization contains invalid characters.')


def validate_qualification(value):
    """
    Validate medical qualification
    """
    if not value or len(value.strip()) < 2:
        raise ValidationError('Qualification must be at least 2 characters long.')
    
    # Check for valid characters (letters, spaces, periods, commas)
    if not re.match(r"^[a-zA-Z\s\-.,()]+$", value):
        raise ValidationError('Qualification contains invalid characters.')
