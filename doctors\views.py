from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import Doctor
from .serializers import Doctor<PERSON>erial<PERSON>, DoctorCreateSerializer, DoctorListSerializer


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def doctor_list_create(request):
    """
    List all doctors or create a new doctor
    """
    if request.method == 'GET':
        doctors = Doctor.objects.all()
        serializer = DoctorListSerializer(doctors, many=True)
        return Response({
            'count': doctors.count(),
            'results': serializer.data
        }, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = DoctorCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            doctor = serializer.save(created_by=request.user)
            return Response({
                'message': 'Doctor created successfully',
                'doctor': DoctorSerializer(doctor).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def doctor_detail(request, pk):
    """
    Retrieve, update or delete a doctor
    """
    doctor = get_object_or_404(Doctor, pk=pk)

    if request.method == 'GET':
        serializer = DoctorSerializer(doctor)
        return Response(serializer.data, status=status.HTTP_200_OK)

    elif request.method == 'PUT':
        # Only the creator can update the doctor
        if doctor.created_by != request.user:
            return Response({
                'error': 'You do not have permission to update this doctor'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = DoctorSerializer(doctor, data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'Doctor updated successfully',
                'doctor': serializer.data
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == 'DELETE':
        # Only the creator can delete the doctor
        if doctor.created_by != request.user:
            return Response({
                'error': 'You do not have permission to delete this doctor'
            }, status=status.HTTP_403_FORBIDDEN)

        doctor.delete()
        return Response({
            'message': 'Doctor deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)
