from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.http import Http404
from django.core.exceptions import PermissionDenied, ValidationError
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)

    if response is not None:
        custom_response_data = {
            'error': True,
            'message': 'An error occurred',
            'details': response.data,
            'status_code': response.status_code
        }

        # Handle different types of errors
        if response.status_code == status.HTTP_400_BAD_REQUEST:
            custom_response_data['message'] = 'Bad request - please check your input'
        elif response.status_code == status.HTTP_401_UNAUTHORIZED:
            custom_response_data['message'] = 'Authentication required'
        elif response.status_code == status.HTTP_403_FORBIDDEN:
            custom_response_data['message'] = 'Permission denied'
        elif response.status_code == status.HTTP_404_NOT_FOUND:
            custom_response_data['message'] = 'Resource not found'
        elif response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED:
            custom_response_data['message'] = 'Method not allowed'
        elif response.status_code >= 500:
            custom_response_data['message'] = 'Internal server error'
            logger.error(f"Server error: {exc}", exc_info=True)

        response.data = custom_response_data

    # Handle Django exceptions that aren't handled by DRF
    elif isinstance(exc, Http404):
        custom_response_data = {
            'error': True,
            'message': 'Resource not found',
            'details': {'detail': 'Not found'},
            'status_code': status.HTTP_404_NOT_FOUND
        }
        response = Response(custom_response_data, status=status.HTTP_404_NOT_FOUND)

    elif isinstance(exc, PermissionDenied):
        custom_response_data = {
            'error': True,
            'message': 'Permission denied',
            'details': {'detail': str(exc)},
            'status_code': status.HTTP_403_FORBIDDEN
        }
        response = Response(custom_response_data, status=status.HTTP_403_FORBIDDEN)

    elif isinstance(exc, ValidationError):
        custom_response_data = {
            'error': True,
            'message': 'Validation error',
            'details': {'detail': exc.messages if hasattr(exc, 'messages') else str(exc)},
            'status_code': status.HTTP_400_BAD_REQUEST
        }
        response = Response(custom_response_data, status=status.HTTP_400_BAD_REQUEST)

    return response
