from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import PatientDoctorMapping
from patients.models import Patient
from .serializers import (
    PatientDoctorMappingSerializer,
    PatientDoctorMappingCreateSerializer,
    PatientDoctorMappingListSerializer
)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def mapping_list_create(request):
    """
    List all patient-doctor mappings for the authenticated user or create a new mapping
    """
    if request.method == 'GET':
        # Get mappings for patients created by the current user
        mappings = PatientDoctorMapping.objects.filter(
            patient__created_by=request.user,
            is_active=True
        )
        serializer = PatientDoctorMappingListSerializer(mappings, many=True)
        return Response({
            'count': mappings.count(),
            'results': serializer.data
        }, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        serializer = PatientDoctorMappingCreateSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            mapping = serializer.save(assigned_by=request.user)
            return Response({
                'message': 'Doctor assigned to patient successfully',
                'mapping': PatientDoctorMappingSerializer(mapping).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def patient_doctors(request, patient_id):
    """
    Get all doctors assigned to a specific patient
    """
    patient = get_object_or_404(Patient, pk=patient_id, created_by=request.user)
    mappings = PatientDoctorMapping.objects.filter(patient=patient, is_active=True)
    serializer = PatientDoctorMappingListSerializer(mappings, many=True)

    return Response({
        'patient': patient.name,
        'doctors_count': mappings.count(),
        'doctors': serializer.data
    }, status=status.HTTP_200_OK)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def mapping_delete(request, pk):
    """
    Remove a doctor from a patient (soft delete by setting is_active=False)
    """
    mapping = get_object_or_404(
        PatientDoctorMapping,
        pk=pk,
        patient__created_by=request.user,
        is_active=True
    )

    mapping.is_active = False
    mapping.save()

    return Response({
        'message': 'Doctor removed from patient successfully'
    }, status=status.HTTP_204_NO_CONTENT)
