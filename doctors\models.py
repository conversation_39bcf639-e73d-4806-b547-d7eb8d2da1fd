from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator


class Doctor(models.Model):
    """
    Doctor model to store doctor information
    """
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    # Basic Information
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone = models.CharField(validators=[phone_regex], max_length=17)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    # Professional Information
    specialization = models.CharField(max_length=255)
    license_number = models.CharField(max_length=50, unique=True)
    years_of_experience = models.PositiveIntegerField()
    qualification = models.Char<PERSON>ield(max_length=255)

    # Contact Information
    clinic_address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=10)

    # Availability
    consultation_fee = models.DecimalField(max_digits=10, decimal_places=2)
    available_days = models.CharField(max_length=255, help_text="e.g., Monday, Tuesday, Wednesday")
    available_hours = models.CharField(max_length=100, help_text="e.g., 9:00 AM - 5:00 PM")

    # System Information
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='doctors'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Dr. {self.name} - {self.specialization}"
