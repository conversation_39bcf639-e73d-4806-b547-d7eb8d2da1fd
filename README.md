# Healthcare Backend System

A comprehensive healthcare backend system built with Django and Django REST Framework, featuring JWT authentication and complete patient-doctor management capabilities.

## Features

- **User Authentication**: JWT-based authentication with registration and login
- **Patient Management**: Full CRUD operations for patient records
- **Doctor Management**: Complete doctor profile management
- **Patient-Doctor Mapping**: Assign and manage doctor-patient relationships
- **Security**: JWT tokens, user-specific data access, and comprehensive validation
- **Error Handling**: Consistent error responses and logging
- **API Documentation**: Complete API documentation with examples

## Technology Stack

- **Backend Framework**: Django 4.2.7
- **API Framework**: Django REST Framework 3.14.0
- **Authentication**: JWT (djangorestframework-simplejwt)
- **Database**: SQLite (development) / PostgreSQL (production ready)
- **Environment Management**: python-decouple
- **CORS**: django-cors-headers

## Project Structure

```
healthcare_backend/
├── healthcare_backend/          # Main project settings
│   ├── settings.py             # Django settings
│   ├── urls.py                 # Main URL configuration
│   ├── exceptions.py           # Custom exception handling
│   └── validators.py           # Custom validation functions
├── authentication/             # User authentication app
│   ├── models.py              # Custom User model
│   ├── serializers.py         # Authentication serializers
│   ├── views.py               # Auth views (register, login)
│   └── urls.py                # Auth URL patterns
├── patients/                   # Patient management app
│   ├── models.py              # Patient model
│   ├── serializers.py         # Patient serializers
│   ├── views.py               # Patient CRUD views
│   └── urls.py                # Patient URL patterns
├── doctors/                    # Doctor management app
│   ├── models.py              # Doctor model
│   ├── serializers.py         # Doctor serializers
│   ├── views.py               # Doctor CRUD views
│   └── urls.py                # Doctor URL patterns
├── mappings/                   # Patient-Doctor mapping app
│   ├── models.py              # PatientDoctorMapping model
│   ├── serializers.py         # Mapping serializers
│   ├── views.py               # Mapping views
│   └── urls.py                # Mapping URL patterns
├── requirements.txt            # Python dependencies
├── .env                       # Environment variables
├── manage.py                  # Django management script
├── test_api.py               # API testing script
└── API_DOCUMENTATION.md       # Detailed API documentation
```

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Step 1: Clone the Repository
```bash
git clone <repository-url>
cd healthcare_backend
```

### Step 2: Create Virtual Environment
```bash
python -m venv healthcare_env
```

### Step 3: Activate Virtual Environment
**Windows:**
```bash
healthcare_env\Scripts\activate
```

**macOS/Linux:**
```bash
source healthcare_env/bin/activate
```

### Step 4: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 5: Environment Configuration
Create a `.env` file in the project root:
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### Step 6: Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### Step 7: Create Superuser (Optional)
```bash
python manage.py createsuperuser
```

### Step 8: Run the Server
```bash
python manage.py runserver
```

The API will be available at `http://127.0.0.1:8000/api/`

## API Endpoints

### Authentication
- `POST /api/auth/register/` - Register a new user
- `POST /api/auth/login/` - Login user

### Patients
- `GET /api/patients/` - List all patients
- `POST /api/patients/` - Create a new patient
- `GET /api/patients/<id>/` - Get patient details
- `PUT /api/patients/<id>/` - Update patient
- `DELETE /api/patients/<id>/` - Delete patient

### Doctors
- `GET /api/doctors/` - List all doctors
- `POST /api/doctors/` - Create a new doctor
- `GET /api/doctors/<id>/` - Get doctor details
- `PUT /api/doctors/<id>/` - Update doctor
- `DELETE /api/doctors/<id>/` - Delete doctor

### Patient-Doctor Mappings
- `GET /api/mappings/` - List all mappings
- `POST /api/mappings/` - Create a new mapping
- `GET /api/mappings/<patient_id>/` - Get doctors for a patient
- `DELETE /api/mappings/<id>/` - Remove doctor from patient

## Testing

### Automated Testing
Run the included test script:
```bash
python test_api.py
```

### Manual Testing with cURL
See `API_DOCUMENTATION.md` for detailed cURL examples.

### Using Postman
1. Import the API endpoints into Postman
2. Set up environment variables for base URL and tokens
3. Test each endpoint according to the documentation

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **User-specific Data**: Users can only access their own patients
- **Input Validation**: Comprehensive validation for all inputs
- **Error Handling**: Consistent error responses
- **CORS Configuration**: Proper CORS setup for frontend integration

## Database Models

### User (Custom)
Extended Django User model with additional fields for healthcare context.

### Patient
Comprehensive patient information including personal details, contact info, and medical history.

### Doctor
Complete doctor profiles with professional information, specializations, and availability.

### PatientDoctorMapping
Many-to-many relationship between patients and doctors with additional metadata.

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| SECRET_KEY | Django secret key | Generated |
| DEBUG | Debug mode | True |
| ALLOWED_HOSTS | Allowed hosts | localhost,127.0.0.1 |
| JWT_ACCESS_TOKEN_LIFETIME | Access token lifetime (minutes) | 60 |
| JWT_REFRESH_TOKEN_LIFETIME | Refresh token lifetime (minutes) | 1440 |
| CORS_ALLOWED_ORIGINS | CORS allowed origins | localhost:3000 |

## Production Deployment

For production deployment:

1. Set `DEBUG=False` in environment variables
2. Configure PostgreSQL database
3. Set up proper secret keys
4. Configure static files serving
5. Set up HTTPS
6. Configure logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please refer to the API documentation or create an issue in the repository.
