#!/usr/bin/env python3
"""
Simple API test script for Healthcare Backend
"""
import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/api"

def test_user_registration():
    """Test user registration"""
    print("Testing user registration...")
    url = f"{BASE_URL}/auth/register/"
    data = {
        "name": "Test User 2",
        "email": "<EMAIL>",
        "username": "testuser2",
        "password": "testpass123",
        "password_confirm": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 201:
            print("✓ User registration successful")
            return response.json()
        else:
            print(f"✗ User registration failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error: {e}")
        return None

def test_user_login():
    """Test user login"""
    print("Testing user login...")
    url = f"{BASE_URL}/auth/login/"
    data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            print("✓ User login successful")
            return response.json()
        else:
            print(f"✗ User login failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error: {e}")
        return None

def test_patient_creation(token):
    """Test patient creation"""
    print("Testing patient creation...")
    url = f"{BASE_URL}/patients/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "name": "Jane Doe",
        "email": "<EMAIL>",
        "phone": "+**********",
        "date_of_birth": "1990-01-01",
        "gender": "F",
        "address": "123 Main St",
        "city": "Boston",
        "state": "MA",
        "zip_code": "02101",
        "blood_group": "A+",
        "allergies": "None",
        "medical_history": "No significant history"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 201:
            print("✓ Patient creation successful")
            return response.json()
        else:
            print(f"✗ Patient creation failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error: {e}")
        return None

def test_doctor_creation(token):
    """Test doctor creation"""
    print("Testing doctor creation...")
    url = f"{BASE_URL}/doctors/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "name": "Dr. Smith",
        "email": "<EMAIL>",
        "phone": "+**********",
        "gender": "M",
        "specialization": "Cardiology",
        "license_number": "MD12345",
        "years_of_experience": 10,
        "qualification": "MD, FACC",
        "clinic_address": "456 Medical Center",
        "city": "Boston",
        "state": "MA",
        "zip_code": "02101",
        "consultation_fee": 200.00,
        "available_days": "Monday, Tuesday, Wednesday",
        "available_hours": "9:00 AM - 5:00 PM"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 201:
            print("✓ Doctor creation successful")
            return response.json()
        else:
            print(f"✗ Doctor creation failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error: {e}")
        return None

def test_patient_doctor_mapping(token, patient_id, doctor_id):
    """Test patient-doctor mapping"""
    print("Testing patient-doctor mapping...")
    url = f"{BASE_URL}/mappings/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "patient": patient_id,
        "doctor": doctor_id,
        "notes": "Regular checkup assignment"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 201:
            print("✓ Patient-doctor mapping successful")
            return response.json()
        else:
            print(f"✗ Patient-doctor mapping failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"✗ Connection error: {e}")
        return None

def main():
    """Main test function"""
    print("Healthcare Backend API Test")
    print("=" * 40)
    
    # Test user registration
    user_data = test_user_registration()
    if not user_data:
        print("Cannot proceed without user registration")
        sys.exit(1)
    
    # Test user login
    login_data = test_user_login()
    if not login_data:
        print("Cannot proceed without user login")
        sys.exit(1)
    
    token = login_data['tokens']['access']
    
    # Test patient creation
    patient_data = test_patient_creation(token)
    if not patient_data:
        print("Patient creation failed")
        return
    
    patient_id = patient_data['patient']['id']
    
    # Test doctor creation
    doctor_data = test_doctor_creation(token)
    if not doctor_data:
        print("Doctor creation failed")
        return
    
    doctor_id = doctor_data['doctor']['id']
    
    # Test patient-doctor mapping
    mapping_data = test_patient_doctor_mapping(token, patient_id, doctor_id)
    
    print("\n" + "=" * 40)
    print("API Test Summary:")
    print("✓ User Registration")
    print("✓ User Login")
    print("✓ Patient Creation")
    print("✓ Doctor Creation")
    if mapping_data:
        print("✓ Patient-Doctor Mapping")
    print("\nAll tests completed successfully!")

if __name__ == "__main__":
    main()
