from rest_framework import serializers
from .models import Doctor


class DoctorSerializer(serializers.ModelSerializer):
    """
    Serializer for Doctor model
    """
    created_by = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = Doctor
        fields = [
            'id', 'name', 'email', 'phone', 'gender', 'specialization',
            'license_number', 'years_of_experience', 'qualification',
            'clinic_address', 'city', 'state', 'zip_code', 'consultation_fee',
            'available_days', 'available_hours', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']

    def validate_license_number(self, value):
        """
        Check that the license number is unique
        """
        if Doctor.objects.filter(license_number=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("A doctor with this license number already exists.")
        return value


class DoctorCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a new doctor
    """
    class Meta:
        model = Doctor
        fields = [
            'name', 'email', 'phone', 'gender', 'specialization',
            'license_number', 'years_of_experience', 'qualification',
            'clinic_address', 'city', 'state', 'zip_code', 'consultation_fee',
            'available_days', 'available_hours'
        ]

    def validate_license_number(self, value):
        """
        Check that the license number is unique
        """
        if Doctor.objects.filter(license_number=value).exists():
            raise serializers.ValidationError("A doctor with this license number already exists.")
        return value


class DoctorListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing doctors (minimal fields)
    """
    class Meta:
        model = Doctor
        fields = [
            'id', 'name', 'email', 'phone', 'specialization',
            'years_of_experience', 'city', 'state', 'consultation_fee',
            'available_days', 'created_at'
        ]
